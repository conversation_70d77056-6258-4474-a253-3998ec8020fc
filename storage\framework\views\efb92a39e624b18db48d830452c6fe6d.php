
<?php $__env->startSection('content'); ?>
<section class="services" id="services">
    <div class="titlebar">
        <h1>Services</h1>
        <button class="btn-icon success open-modal">New Service</button>
    </div>
    <?php echo $__env->make('admin.services.create', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <?php echo $__env->make('includes.flash_message', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <div class="table">
        <div class="table-filter">
            <div>
                <ul class="table-filter-list">
                    <li>
                        <p class="table-filter-link link-active">All</p>
                    </li>
                </ul>
            </div>
        </div>
        <div class="table-search">
            <div>
                <select class="search-select" name="" id="">
                    <option value="">Filter Service</option>
                </select>
            </div>
            <div class="relative">
                <input class="search-input" type="text" name="search" placeholder="Search Service...">
            </div>
        </div>
        <div class="service_table-heading">
            <p>Title</p>
            <p>Icon</p>
            <p>Description</p>
            <p>Actions</p>
        </div>
        <!-- item 1 -->
        <?php $__currentLoopData = $services; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <div class="service_table-items">
            <p><?php echo e($service->name); ?></p>
            <button class="service_table-icon">
                <i class="uil <?php echo e($service->icon); ?>"></i>
            </button>
            <p><?php echo e($service->description); ?></p>
            <div>
                <button class="btn-icon success edit-service-btn" data-id="<?php echo e($service->id); ?>" data-name="<?php echo e($service->name); ?>" data-icon="<?php echo e($service->icon); ?>" data-description="<?php echo e($service->description); ?>">
                    <i class="fas fa-pencil-alt"></i>
                </button>
                    <form method="POST" action="<?php echo e(route('admin.services.destroy', $service->id)); ?>" style="display: inline;" class="delete-form">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('DELETE'); ?>
                        <button type="button" class="btn-icon danger delete-btn" data-service-name="<?php echo e($service->name); ?>">
                            <i class="far fa-trash-alt"></i>
                        </button>
                    </form>
            </div>
        </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

    </div>

    <!-- Edit Service Modal -->
    <form method="POST" action="" id="edit-service-form">
        <?php echo csrf_field(); ?>
        <?php echo method_field('PATCH'); ?>
        <div class="modal" id="edit-service-modal">
            <div class="modal-content">
                <h2>Edit Service</h2>
                <span class="close-modal">×</span>
                <hr>
                <div>
                    <label>Service Name</label>
                    <input type="text" name='name' id="edit-service-name" value=""/>

                    <label>Icon Class <span style="color:#006fbb;">(Find your suitable icon: <a href="https://fontawesome.com/icons" target="_blank">Font Awesome</a>)</span></label>
                    <small style="color:#666;">Examples: fa-code, fa-server, fa-laptop-code, fa-paint-brush, fa-mobile-alt</small>
                    <input type="text" name='icon' id="edit-service-icon" value="" placeholder="fa-code"/>

                    <label>Description</label>
                    <textarea cols="10" rows="5" name='description' id="edit-service-description"></textarea>
                </div>
                <hr>
                <div class="modal-footer">
                    <button type="button" class="close-modal">
                        Cancel
                    </button>
                    <button type="submit" class="secondary">
                        <span><i class="fa fa-spinner fa-spin"></i></span>
                        Update
                    </button>
                </div>
            </div>
        </div>
    </form>

</section>

    <!-- Hidden input to detect validation errors for create form -->
    <?php if($errors->any() && !request()->has('edit')): ?>
        <input type="hidden" id="has-create-errors" value="1">
    <?php endif; ?>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin.base', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\inetpub\wwwroot\Laravel-Portfolio\resources\views/admin/services/index.blade.php ENDPATH**/ ?>